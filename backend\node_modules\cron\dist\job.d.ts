import { CronTime } from './time';
import { <PERSON>ron<PERSON>allback, <PERSON>ron<PERSON>ontext, CronJobParams, CronOnCompleteCallback, CronOnCompleteCommand, WithOnComplete } from './types/cron.types';
export declare class CronJob<OC extends CronOnCompleteCommand | null = null, C = null> {
    cronTime: CronTime;
    unrefTimeout: boolean;
    lastExecution: Date | null;
    runOnce: boolean;
    context: CronContext<C>;
    onComplete?: WithOnComplete<OC> extends true ? CronOnCompleteCallback : undefined;
    waitForCompletion: boolean;
    errorHandler?: CronJobParams<OC, C>['errorHandler'];
    name?: string;
    threshold: number;
    private _isActive;
    private _isCallbackRunning;
    private _timeout?;
    private _callbacks;
    get isActive(): boolean;
    get isCallbackRunning(): boolean;
    constructor(cronTime: CronJobParams<OC, C>['cronTime'], onTick: CronJobParams<OC, C>['onTick'], onComplete?: CronJobParams<OC, C>['onComplete'], start?: CronJobParams<OC, C>['start'], timeZone?: CronJobParams<OC, C>['timeZone'], context?: CronJobParams<OC, C>['context'], runOnInit?: CronJobParams<OC, C>['runOnInit'], utcOffset?: null, unrefTimeout?: CronJobParams<OC, C>['unrefTimeout'], waitForCompletion?: CronJobParams<OC, C>['waitForCompletion'], errorHandler?: CronJobParams<OC, C>['errorHandler'], name?: CronJobParams<OC, C>['name'], threshold?: CronJobParams<OC, C>['threshold']);
    constructor(cronTime: CronJobParams<OC, C>['cronTime'], onTick: CronJobParams<OC, C>['onTick'], onComplete?: CronJobParams<OC, C>['onComplete'], start?: CronJobParams<OC, C>['start'], timeZone?: null, context?: CronJobParams<OC, C>['context'], runOnInit?: CronJobParams<OC, C>['runOnInit'], utcOffset?: CronJobParams<OC, C>['utcOffset'], unrefTimeout?: CronJobParams<OC, C>['unrefTimeout'], waitForCompletion?: CronJobParams<OC, C>['waitForCompletion'], errorHandler?: CronJobParams<OC, C>['errorHandler'], name?: CronJobParams<OC, C>['name'], threshold?: CronJobParams<OC, C>['threshold']);
    static from<OC extends CronOnCompleteCommand | null = null, C = null>(params: CronJobParams<OC, C>): CronJob<OC, C>;
    private _fnWrap;
    addCallback(callback: CronCallback<C, WithOnComplete<OC>>): void;
    setTime(time: CronTime): void;
    nextDate(): import("luxon").DateTime<boolean>;
    fireOnTick(): Promise<void>;
    nextDates(i?: number): import("luxon").DateTime<boolean>[];
    start(): void;
    lastDate(): Date | null;
    private _executeOnComplete;
    private _waitForJobCompletion;
    stop(): Promise<void> | undefined;
}
